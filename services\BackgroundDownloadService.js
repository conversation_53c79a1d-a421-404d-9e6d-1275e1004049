import * as FileSystem from 'expo-file-system';
import * as MediaLibrary from 'expo-media-library';
import AsyncStorage from '@react-native-async-storage/async-storage';
import VideoDownloadService from './VideoDownloadService';
import DownloadTracker from './DownloadTracker';
import DownloadNotificationService from './DownloadNotificationService';

// Serviço para downloads em background
class BackgroundDownloadService {
  static activeDownloads = new Map(); // Controle de downloads ativos
  // Removida fila - downloads são simultâneos agora

  // Iniciar download em background
  static async startBackgroundDownload(downloadInfo) {
    // Limpar downloads antigos primeiro
    await this.clearOldDownloads();

    const downloadId = this.generateDownloadId(downloadInfo);

    // Verificar apenas se já foi baixado anteriormente (não bloquear downloads simultâneos)
    const trackerCheck = await DownloadTracker.checkDownloadExists(downloadInfo);
    if (trackerCheck.exists) {
      console.log('🚫 Download duplicado detectado:', trackerCheck.reason);
      throw new Error('Este vídeo já foi baixado');
    }

    // Se já está sendo baixado, gerar um ID único para permitir download simultâneo
    if (this.activeDownloads.has(downloadId)) {
      console.log('📥 Download similar em progresso, criando novo ID para download simultâneo');
      downloadId = `${downloadId}_${Date.now()}`;
    }

    try {

      // Criar task de download
      const downloadTask = {
        id: downloadId,
        ...downloadInfo,
        status: 'starting',
        progress: 0,
        startTime: new Date().toISOString(),
        error: null,
        normalizedUrl: this.normalizeVideoUrl(downloadInfo.url || downloadInfo.originalUrl)
      };

      this.activeDownloads.set(downloadId, downloadTask);

      // Salvar estado
      await this.saveDownloadState();

      // Iniciar download imediatamente (sem fila)
      this.executeDownloadImmediately(downloadTask);

      return downloadId;

    } catch (error) {
      console.error('❌ Erro ao iniciar download:', error);
      throw error;
    }
  }









  // Gerar ID único para download baseado em URL normalizada
  static generateDownloadId(downloadInfo) {
    const url = downloadInfo.url || downloadInfo.originalUrl;
    const quality = downloadInfo.quality?.quality || 'default';

    // Normalizar URL para detectar duplicatas
    const normalizedUrl = this.normalizeVideoUrl(url);

    // Usar URL normalizada + qualidade para gerar ID consistente
    const idString = `${normalizedUrl}|${quality}`;
    const urlHash = this.generateUrlHash(idString);

    console.log('🆔 Gerando ID para:', { url, normalizedUrl, quality, hash: urlHash });

    return `${urlHash}_${quality}`.replace(/[^a-zA-Z0-9_]/g, '_').substring(0, 50);
  }

  // Normalizar URL para comparação de duplicatas
  static normalizeVideoUrl(url) {
    if (!url) return url;

    try {
      // Limpar URL básica primeiro
      let cleanUrl = url.trim();

      // Se não é uma URL válida, retornar como está
      if (!cleanUrl.startsWith('http')) {
        return cleanUrl;
      }

      const urlObj = new URL(cleanUrl);

      // Remover parâmetros desnecessários (mais abrangente)
      const paramsToRemove = [
        'utm_source', 'utm_medium', 'utm_campaign', 'utm_content', 'utm_term',
        'fbclid', 'gclid', 'msclkid', 'igshid', 'ref', 'ref_src', 'ref_url',
        't', 'si', 'feature', 'app', 'via', 'source', 'from'
      ];
      paramsToRemove.forEach(param => urlObj.searchParams.delete(param));

      // Normalizar YouTube URLs (mais robusto)
      if (urlObj.hostname.includes('youtube.com') || urlObj.hostname.includes('youtu.be')) {
        let videoId = null;

        if (urlObj.hostname.includes('youtu.be')) {
          videoId = urlObj.pathname.split('/')[1]?.split('?')[0];
        } else if (urlObj.searchParams.has('v')) {
          videoId = urlObj.searchParams.get('v');
        } else if (urlObj.pathname.includes('/embed/')) {
          videoId = urlObj.pathname.split('/embed/')[1]?.split('?')[0];
        }

        if (videoId && videoId.length === 11) {
          return `https://www.youtube.com/watch?v=${videoId}`;
        }
      }

      // Normalizar Vimeo URLs (mais robusto)
      if (urlObj.hostname.includes('vimeo.com')) {
        const pathParts = urlObj.pathname.split('/').filter(p => p);
        const videoId = pathParts.find(p => /^\d+$/.test(p));
        if (videoId) {
          return `https://vimeo.com/${videoId}`;
        }
      }

      // Normalizar outras plataformas conhecidas
      if (urlObj.hostname.includes('dailymotion.com')) {
        const videoMatch = urlObj.pathname.match(/\/video\/([^_?]+)/);
        if (videoMatch) {
          return `https://www.dailymotion.com/video/${videoMatch[1]}`;
        }
      }

      // Para URLs diretas de vídeo, manter estrutura básica
      if (/\.(mp4|webm|avi|mov|mkv|flv|wmv|m4v)(\?|$)/i.test(urlObj.pathname)) {
        return `${urlObj.protocol}//${urlObj.hostname}${urlObj.pathname}`;
      }

      // Para outras URLs, remover apenas parâmetros de tracking
      return urlObj.toString();
    } catch (e) {
      console.warn('Erro ao normalizar URL:', url, e.message);
      return url;
    }
  }

  // Gerar hash da URL para identificação única
  static generateUrlHash(input) {
    if (!input) return 'empty';

    // Usar algoritmo de hash mais robusto
    let hash = 0;
    const str = input.toString();

    if (str.length === 0) return 'empty';

    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }

    // Garantir que o hash seja sempre positivo e consistente
    const positiveHash = Math.abs(hash);
    return positiveHash.toString(36).padStart(6, '0');
  }

  // Executar download imediatamente (sem fila)
  static async executeDownloadImmediately(downloadTask) {
    console.log(`🚀 Iniciando download simultâneo: ${downloadTask.id}`);

    // Executar em background sem bloquear outros downloads
    setTimeout(async () => {
      try {
        await this.executeDownload(downloadTask);
      } catch (error) {
        console.error(`❌ Erro no download ${downloadTask.id}:`, error);

        // Criar cópia mutável para atualizar erro
        const errorTask = { ...downloadTask };
        errorTask.status = 'error';
        errorTask.error = error.message;

        // Atualizar no Map
        this.activeDownloads.set(errorTask.id, errorTask);
        await this.saveDownloadState();
      }
    }, 100); // Pequeno delay para não bloquear a UI
  }

  // Executar download individual
  static async executeDownload(downloadTask) {
    // Criar uma cópia mutável do downloadTask para evitar erro "read only"
    const mutableTask = { ...downloadTask };

    try {
      mutableTask.status = 'downloading';

      // Atualizar no Map com a cópia mutável
      this.activeDownloads.set(mutableTask.id, mutableTask);
      await this.saveDownloadState();

      // Gerar nome do arquivo
      const fileName = this.generateFileName(downloadTask);
      const fileUri = FileSystem.documentDirectory + fileName;

      // Callback de progresso
      const progressCallback = async (progress) => {
        const percentage = Math.round((progress.totalBytesWritten / progress.totalBytesExpectedToWrite) * 100);

        // Atualizar a cópia mutável
        mutableTask.progress = percentage;
        mutableTask.status = 'downloading';

        // Atualizar no Map
        this.activeDownloads.set(mutableTask.id, mutableTask);

        // Salvar progresso a cada 10%
        if (percentage % 10 === 0) {
          await this.saveDownloadState();
        }
      };

      // Usar VideoDownloadService para fazer o download real
      const result = await VideoDownloadService.downloadVideo(
        mutableTask.url,
        progressCallback
      );

      if (!result || !result.uri) {
        throw new Error('Falha no download do arquivo');
      }

      // Salvar na galeria usando o VideoDownloadService
      try {
        await VideoDownloadService.saveToGallery(result.uri, result.fileName || fileName);
      } catch (galleryError) {
        // Se falhar ao salvar na galeria, continuar mesmo assim
        console.warn('Falha ao salvar na galeria:', galleryError.message);
      }

      // Atualizar status na cópia mutável
      mutableTask.status = 'completed';
      mutableTask.progress = 100;
      mutableTask.fileUri = result.uri;
      mutableTask.fileName = result.fileName || fileName;
      mutableTask.fileSize = result.size;
      mutableTask.completedTime = new Date().toISOString();

      // Atualizar no Map
      this.activeDownloads.set(mutableTask.id, mutableTask);

      // Adicionar ao histórico de downloads
      await this.addToDownloadHistory(mutableTask);

      // Adicionar ao DownloadTracker
      await DownloadTracker.addDownloadRecord(mutableTask);

    } catch (error) {
      // Usar a cópia mutável para definir erro
      mutableTask.status = 'error';
      mutableTask.error = error.message;

      // Atualizar no Map
      this.activeDownloads.set(mutableTask.id, mutableTask);
      throw error;
    } finally {
      await this.saveDownloadState();
    }
  }

  // Gerar nome do arquivo
  static generateFileName(downloadTask) {
    const quality = downloadTask.quality?.quality || 'default';
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const extension = this.getFileExtension(downloadTask.url);
    
    return `video_${quality}_${timestamp}.${extension}`;
  }

  // Obter extensão do arquivo
  static getFileExtension(url) {
    if (url.includes('.mp4')) return 'mp4';
    if (url.includes('.webm')) return 'webm';
    if (url.includes('.avi')) return 'avi';
    if (url.includes('.mov')) return 'mov';
    return 'mp4'; // padrão
  }

  // Salvar na galeria
  static async saveToGallery(fileUri, fileName) {
    try {
      const asset = await MediaLibrary.createAssetAsync(fileUri);
      const album = await MediaLibrary.getAlbumAsync('Video Downloads');
      
      if (album == null) {
        await MediaLibrary.createAlbumAsync('Video Downloads', asset, false);
      } else {
        await MediaLibrary.addAssetsToAlbumAsync([asset], album, false);
      }
      
      console.log('✅ Vídeo salvo na galeria:', fileName);
    } catch (error) {
      console.error('❌ Erro ao salvar na galeria:', error);
      throw error;
    }
  }

  // Salvar estado dos downloads
  static async saveDownloadState() {
    try {
      const state = {
        activeDownloads: Array.from(this.activeDownloads.values()),
        lastUpdate: new Date().toISOString()
      };
      
      await AsyncStorage.setItem('background_downloads', JSON.stringify(state));
    } catch (error) {
      console.error('Erro ao salvar estado dos downloads:', error);
    }
  }

  // Carregar estado dos downloads
  static async loadDownloadState() {
    try {
      const stateJson = await AsyncStorage.getItem('background_downloads');
      if (stateJson) {
        const state = JSON.parse(stateJson);
        
        // Restaurar downloads ativos
        this.activeDownloads.clear();
        state.activeDownloads?.forEach(download => {
          this.activeDownloads.set(download.id, download);
        });

        console.log(`📂 Estado restaurado: ${this.activeDownloads.size} downloads ativos`);
      }
    } catch (error) {
      console.error('Erro ao carregar estado dos downloads:', error);
    }
  }

  // Adicionar ao histórico
  static async addToDownloadHistory(downloadTask) {
    try {
      const historyJson = await AsyncStorage.getItem('video_downloads');
      const history = historyJson ? JSON.parse(historyJson) : [];
      
      const historyItem = {
        id: downloadTask.id,
        fileName: downloadTask.fileName,
        uri: downloadTask.fileUri,
        size: downloadTask.fileSize,
        quality: downloadTask.quality,
        originalUrl: downloadTask.originalUrl,
        timestamp: downloadTask.completedTime,
        status: 'completed'
      };
      
      history.unshift(historyItem);
      await AsyncStorage.setItem('video_downloads', JSON.stringify(history.slice(0, 100))); // Manter apenas 100
      
    } catch (error) {
      console.error('Erro ao adicionar ao histórico:', error);
    }
  }

  // Obter status de todos os downloads
  static getDownloadStatus() {
    return {
      active: Array.from(this.activeDownloads.values()),
      simultaneous: true // Downloads são simultâneos agora
    };
  }

  // Cancelar download
  static async cancelDownload(downloadId) {
    const download = this.activeDownloads.get(downloadId);
    if (download) {
      download.status = 'cancelled';
      this.activeDownloads.delete(downloadId);
      
      // Downloads simultâneos - não há fila para remover
      
      await this.saveDownloadState();
      return true;
    }
    return false;
  }

  // Limpar downloads concluídos
  static async clearCompletedDownloads() {
    const completed = Array.from(this.activeDownloads.values()).filter(d =>
      d.status === 'completed' || d.status === 'error'
    );

    completed.forEach(download => {
      this.activeDownloads.delete(download.id);
    });

    await this.saveDownloadState();
    return completed.length;
  }

  // Limpar downloads antigos e órfãos (melhorado)
  static async clearOldDownloads() {
    const now = Date.now();
    const maxAge = 30 * 60 * 1000; // 30 minutos (reduzido de 1 hora)
    const stuckThreshold = 10 * 60 * 1000; // 10 minutos para downloads travados

    let removedCount = 0;

    // Limpar downloads ativos antigos, travados ou concluídos
    const toRemove = [];
    for (const [id, download] of this.activeDownloads) {
      const startTime = new Date(download.startTime).getTime();
      const isOld = now - startTime > maxAge;
      const isStuck = download.status === 'downloading' && now - startTime > stuckThreshold;
      const isCompleted = download.status === 'completed' || download.status === 'error';

      if (isOld || isStuck || isCompleted) {
        toRemove.push(id);
        console.log(`🧹 Removendo download ${isOld ? 'antigo' : isStuck ? 'travado' : 'concluído'}:`, id);
      }
    }

    toRemove.forEach(id => {
      this.activeDownloads.delete(id);
      removedCount++;
    });

    // Downloads simultâneos - não há fila para limpar

    console.log(`🧹 Limpeza concluída: ${removedCount} downloads órfãos removidos`);

    if (removedCount > 0) {
      await this.saveDownloadState();
    }
  }

  // Inicializar serviço
  static async initialize() {
    await this.loadDownloadState();
    
    // Retomar downloads pendentes
    if (this.downloadQueue.length > 0) {
      console.log(`🔄 Retomando ${this.downloadQueue.length} downloads pendentes`);
      this.processDownloadQueue();
    }
  }
}

export default BackgroundDownloadService;
