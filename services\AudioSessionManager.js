import { Audio } from 'expo-av';
import AsyncStorage from '@react-native-async-storage/async-storage';

/**
 * Audio Session Manager for Concurrent Video Playback
 * Manages audio session configuration to allow multiple videos to play simultaneously
 * without interrupting other device audio
 */
class AudioSessionManager {
  constructor() {
    this.currentMode = null;
    this.isInitialized = false;
    this.audioMixingMode = 'mix'; // 'mix', 'duck', 'exclusive'
  }

  /**
   * Initialize audio session with concurrent playback support
   */
  async initialize() {
    if (this.isInitialized) return;

    try {
      console.log('[AUDIO_SESSION] Initializing audio session for concurrent playback');
      
      // Load saved audio mixing preference, default to 'mix' for concurrent playback
      const savedMode = await AsyncStorage.getItem('audioMixingMode');
      this.audioMixingMode = savedMode || 'mix';

      // Set initial audio mode for concurrent playback
      await this.setAudioMode(this.audioMixingMode);
      
      this.isInitialized = true;
      console.log('[AUDIO_SESSION] Audio session initialized successfully');
    } catch (error) {
      console.error('[AUDIO_SESSION] Failed to initialize audio session:', error);
      throw error;
    }
  }

  /**
   * Set audio mixing mode
   * @param {string} mode - 'mix', 'duck', or 'exclusive'
   */
  async setAudioMode(mode) {
    try {
      console.log('[AUDIO_SESSION] Setting audio mode to:', mode);

      let audioMode;

      // Use safe constants with fallbacks
      const INTERRUPTION_MODE_IOS_MIX_WITH_OTHERS = Audio.InterruptionModeIOS?.MixWithOthers || 0;
      const INTERRUPTION_MODE_IOS_DUCK_OTHERS = Audio.InterruptionModeIOS?.DuckOthers || 1;
      const INTERRUPTION_MODE_IOS_DO_NOT_MIX = Audio.InterruptionModeIOS?.DoNotMix || 2;

      const INTERRUPTION_MODE_ANDROID_DO_NOT_MIX = Audio.InterruptionModeAndroid?.DoNotMix || 1;
      const INTERRUPTION_MODE_ANDROID_DUCK_OTHERS = Audio.InterruptionModeAndroid?.DuckOthers || 2;

      switch (mode) {
        case 'mix':
          // Mix with other audio - allows concurrent playback
          audioMode = {
            allowsRecordingIOS: false,
            interruptionModeIOS: INTERRUPTION_MODE_IOS_MIX_WITH_OTHERS,
            playsInSilentModeIOS: true,
            staysActiveInBackground: true,
            interruptionModeAndroid: INTERRUPTION_MODE_ANDROID_DUCK_OTHERS,
            shouldDuckAndroid: false,
            playThroughEarpieceAndroid: false,
          };
          break;

        case 'duck':
          // Duck other audio - lower volume of other apps
          audioMode = {
            allowsRecordingIOS: false,
            interruptionModeIOS: INTERRUPTION_MODE_IOS_DUCK_OTHERS,
            playsInSilentModeIOS: true,
            staysActiveInBackground: true,
            interruptionModeAndroid: INTERRUPTION_MODE_ANDROID_DUCK_OTHERS,
            shouldDuckAndroid: true,
            playThroughEarpieceAndroid: false,
          };
          break;

        case 'exclusive':
          // Take exclusive control - pause other audio
          audioMode = {
            allowsRecordingIOS: false,
            interruptionModeIOS: INTERRUPTION_MODE_IOS_DO_NOT_MIX,
            playsInSilentModeIOS: true,
            staysActiveInBackground: true,
            interruptionModeAndroid: INTERRUPTION_MODE_ANDROID_DO_NOT_MIX,
            shouldDuckAndroid: false,
            playThroughEarpieceAndroid: false,
          };
          break;

        default:
          throw new Error(`Invalid audio mode: ${mode}`);
      }

      await Audio.setAudioModeAsync(audioMode);
      this.currentMode = audioMode;
      this.audioMixingMode = mode;

      // Save preference
      await AsyncStorage.setItem('audioMixingMode', mode);

      console.log('[AUDIO_SESSION] Audio mode set successfully');
    } catch (error) {
      console.error('[AUDIO_SESSION] Failed to set audio mode:', error);
      throw error;
    }
  }

  /**
   * Get current audio mixing mode
   */
  getAudioMixingMode() {
    return this.audioMixingMode;
  }

  /**
   * Get available audio mixing modes with descriptions
   */
  getAvailableModes() {
    return [
      {
        id: 'mix',
        name: 'Mix with Other Audio',
        description: 'Play videos alongside other apps\' audio (music, podcasts, etc.)',
        icon: 'musical-notes'
      },
      {
        id: 'duck',
        name: 'Duck Other Audio',
        description: 'Lower volume of other apps when playing videos',
        icon: 'volume-medium'
      },
      {
        id: 'exclusive',
        name: 'Exclusive Control',
        description: 'Pause other apps\' audio when playing videos',
        icon: 'volume-high'
      }
    ];
  }

  /**
   * Prepare audio session for video playback
   */
  async prepareForVideoPlayback() {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }
      
      console.log('[AUDIO_SESSION] Preparing for video playback');
      // Audio session is already configured for concurrent playback
      return true;
    } catch (error) {
      console.error('[AUDIO_SESSION] Failed to prepare for video playback:', error);
      return false;
    }
  }

  /**
   * Handle app going to background
   */
  async handleAppBackground() {
    try {
      console.log('[AUDIO_SESSION] App going to background - maintaining audio session');
      // Audio session is configured to stay active in background
    } catch (error) {
      console.error('[AUDIO_SESSION] Error handling app background:', error);
    }
  }

  /**
   * Handle app coming to foreground
   */
  async handleAppForeground() {
    try {
      console.log('[AUDIO_SESSION] App coming to foreground');
      // Ensure audio session is still properly configured
      if (this.currentMode) {
        await this.setAudioMode(this.audioMixingMode);
      }
    } catch (error) {
      console.error('[AUDIO_SESSION] Error handling app foreground:', error);
    }
  }

  /**
   * Reset audio session to default
   */
  async reset() {
    try {
      console.log('[AUDIO_SESSION] Resetting audio session');

      const INTERRUPTION_MODE_IOS_MIX_WITH_OTHERS = Audio.InterruptionModeIOS?.MixWithOthers || 0;
      const INTERRUPTION_MODE_ANDROID_DUCK_OTHERS = Audio.InterruptionModeAndroid?.DuckOthers || 2;

      await Audio.setAudioModeAsync({
        allowsRecordingIOS: false,
        interruptionModeIOS: INTERRUPTION_MODE_IOS_MIX_WITH_OTHERS,
        playsInSilentModeIOS: false,
        staysActiveInBackground: false,
        interruptionModeAndroid: INTERRUPTION_MODE_ANDROID_DUCK_OTHERS,
        shouldDuckAndroid: true,
        playThroughEarpieceAndroid: false,
      });

      this.currentMode = null;
      this.isInitialized = false;
    } catch (error) {
      console.error('[AUDIO_SESSION] Failed to reset audio session:', error);
    }
  }
}

// Export singleton instance
export default new AudioSessionManager();
