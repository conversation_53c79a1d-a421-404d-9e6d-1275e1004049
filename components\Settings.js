import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert,
  Modal,
  Dimensions
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';

const { width } = Dimensions.get('window');

const Settings = ({ visible, onClose }) => {
  const [settings, setSettings] = useState({
    adBlockEnabled: true,
    trackingProtection: true,
    cookieBlocking: false,
    javascriptEnabled: true,
    imagesEnabled: true,
    popupBlocking: true,
    privateBrowsing: false,
    autoDownloadQuality: 'best',
    downloadLocation: 'gallery',
    clearDataOnExit: false,
    audioMode: 'mix',
    concurrentVideoPlayback: true,
  });

  const [showAbout, setShowAbout] = useState(false);
  const [showQualityModal, setShowQualityModal] = useState(false);
  const [showAudioModal, setShowAudioModal] = useState(false);
  const [showDownloadLocationModal, setShowDownloadLocationModal] = useState(false);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const savedSettings = await AsyncStorage.getItem('browser_settings');
      if (savedSettings) {
        setSettings({ ...settings, ...JSON.parse(savedSettings) });
      }
    } catch (error) {
      console.error('Erro ao carregar configurações:', error);
    }
  };

  const saveSettings = async (newSettings) => {
    try {
      await AsyncStorage.setItem('browser_settings', JSON.stringify(newSettings));
      setSettings(newSettings);
    } catch (error) {
      console.error('Erro ao salvar configurações:', error);
    }
  };

  const updateSetting = (key, value) => {
    const newSettings = { ...settings, [key]: value };
    saveSettings(newSettings);
  };

  const clearBrowsingData = () => {
    Alert.alert(
      'Limpar Dados de Navegação',
      'Isso irá remover histórico, cookies, cache e dados de sites. Continuar?',
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Limpar',
          style: 'destructive',
          onPress: async () => {
            try {
              await AsyncStorage.multiRemove([
                'browser_history',
                'browser_bookmarks',
                'browser_cookies'
              ]);
              Alert.alert('Sucesso', 'Dados de navegação limpos');
            } catch (error) {
              Alert.alert('Erro', 'Não foi possível limpar os dados');
            }
          }
        }
      ]
    );
  };

  const clearDownloads = () => {
    Alert.alert(
      'Limpar Lista de Downloads',
      'Isso irá remover a lista de downloads (os arquivos não serão excluídos). Continuar?',
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Limpar',
          style: 'destructive',
          onPress: async () => {
            try {
              await AsyncStorage.removeItem('video_downloads');
              Alert.alert('Sucesso', 'Lista de downloads limpa');
            } catch (error) {
              Alert.alert('Erro', 'Não foi possível limpar a lista');
            }
          }
        }
      ]
    );
  };

  const SettingItem = ({ 
    icon, 
    title, 
    subtitle, 
    value, 
    onValueChange, 
    type = 'switch',
    onPress 
  }) => (
    <TouchableOpacity 
      style={styles.settingItem}
      onPress={onPress}
      disabled={type === 'switch'}
    >
      <View style={styles.settingIcon}>
        <Ionicons name={icon} size={24} color="#6c5ce7" />
      </View>
      
      <View style={styles.settingContent}>
        <Text style={styles.settingTitle}>{title}</Text>
        {subtitle && <Text style={styles.settingSubtitle}>{subtitle}</Text>}
      </View>
      
      {type === 'switch' && (
        <Switch
          value={value}
          onValueChange={onValueChange}
          trackColor={{ false: '#2d2d44', true: '#6c5ce7' }}
          thumbColor={value ? '#fff' : '#888'}
        />
      )}
      
      {type === 'arrow' && (
        <Ionicons name="chevron-forward" size={20} color="#888" />
      )}
    </TouchableOpacity>
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose}>
            <Ionicons name="arrow-back" size={24} color="#fff" />
          </TouchableOpacity>
          <Text style={styles.title}>Configurações</Text>
          <View style={{ width: 24 }} />
        </View>

        <ScrollView style={styles.content}>
          {/* Seção de Privacidade */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Privacidade e Segurança</Text>
            
            <SettingItem
              icon="shield-checkmark"
              title="Bloqueador de Anúncios"
              subtitle="Bloquear anúncios e rastreadores"
              value={settings.adBlockEnabled}
              onValueChange={(value) => updateSetting('adBlockEnabled', value)}
            />
            
            <SettingItem
              icon="eye-off"
              title="Proteção contra Rastreamento"
              subtitle="Impedir sites de rastrear sua atividade"
              value={settings.trackingProtection}
              onValueChange={(value) => updateSetting('trackingProtection', value)}
            />
            
            <SettingItem
              icon="ban"
              title="Bloquear Cookies"
              subtitle="Bloquear cookies de terceiros"
              value={settings.cookieBlocking}
              onValueChange={(value) => updateSetting('cookieBlocking', value)}
            />
            
            <SettingItem
              icon="close-circle"
              title="Bloquear Pop-ups"
              subtitle="Impedir janelas pop-up"
              value={settings.popupBlocking}
              onValueChange={(value) => updateSetting('popupBlocking', value)}
            />
            
            <SettingItem
              icon="glasses"
              title="Navegação Privada"
              subtitle="Não salvar histórico nem cookies"
              value={settings.privateBrowsing}
              onValueChange={(value) => updateSetting('privateBrowsing', value)}
            />
          </View>

          {/* Seção de Conteúdo */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Conteúdo</Text>
            
            <SettingItem
              icon="code-slash"
              title="JavaScript"
              subtitle="Permitir execução de JavaScript"
              value={settings.javascriptEnabled}
              onValueChange={(value) => updateSetting('javascriptEnabled', value)}
            />
            
            <SettingItem
              icon="image"
              title="Imagens"
              subtitle="Carregar imagens automaticamente"
              value={settings.imagesEnabled}
              onValueChange={(value) => updateSetting('imagesEnabled', value)}
            />
          </View>

          {/* Seção de Downloads */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Downloads</Text>
            
            <SettingItem
              icon="download"
              title="Qualidade Automática"
              subtitle={`Padrão: ${settings.autoDownloadQuality}`}
              type="arrow"
              onPress={() => setShowQualityModal(true)}
            />
            
            <SettingItem
              icon="folder"
              title="Local de Download"
              subtitle="Salvar na galeria do dispositivo"
              type="arrow"
              onPress={() => setShowDownloadLocationModal(true)}
            />
          </View>

          {/* Seção de Dados */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Dados</Text>
            
            <SettingItem
              icon="trash"
              title="Limpar Dados de Navegação"
              subtitle="Histórico, cookies, cache"
              type="arrow"
              onPress={clearBrowsingData}
            />
            
            <SettingItem
              icon="list"
              title="Limpar Lista de Downloads"
              subtitle="Remove apenas a lista, não os arquivos"
              type="arrow"
              onPress={clearDownloads}
            />
            
            <SettingItem
              icon="exit"
              title="Limpar ao Sair"
              subtitle="Limpar dados automaticamente ao fechar"
              value={settings.clearDataOnExit}
              onValueChange={(value) => updateSetting('clearDataOnExit', value)}
            />
          </View>

          {/* Seção de Vídeo */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Reprodução de Vídeo</Text>

            <SettingItem
              icon="play-circle"
              title="Reprodução Simultânea"
              subtitle="Permitir múltiplos vídeos ao mesmo tempo"
              value={settings.concurrentVideoPlayback}
              onValueChange={(value) => updateSetting('concurrentVideoPlayback', value)}
            />

            <SettingItem
              icon="musical-notes"
              title="Modo de Áudio"
              subtitle={`Atual: ${settings.audioMode || 'Mix'}`}
              type="arrow"
              onPress={() => setShowAudioModal(true)}
            />
          </View>

          {/* Seção Sobre */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Sobre</Text>
            
            <SettingItem
              icon="information-circle"
              title="Sobre o Aplicativo"
              subtitle="Versão 1.0.0"
              type="arrow"
              onPress={() => setShowAbout(true)}
            />
            
            <SettingItem
              icon="help-circle"
              title="Ajuda e Suporte"
              subtitle="Obter ajuda"
              type="arrow"
            />
          </View>
        </ScrollView>

        {/* Quality Selection Modal */}
        <Modal
          visible={showQualityModal}
          animationType="slide"
          transparent={true}
          onRequestClose={() => setShowQualityModal(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.qualityModal}>
              <View style={styles.qualityHeader}>
                <Text style={styles.qualityTitle}>Qualidade Padrão</Text>
                <TouchableOpacity onPress={() => setShowQualityModal(false)}>
                  <Ionicons name="close" size={24} color="#fff" />
                </TouchableOpacity>
              </View>

              <Text style={styles.qualitySubtitle}>
                Escolha a qualidade padrão para downloads automáticos
              </Text>

              <View style={styles.qualityOptions}>
                <TouchableOpacity
                  style={[styles.qualityOption, settings.autoDownloadQuality === 'best' && styles.qualityOptionSelected]}
                  onPress={() => {
                    updateSetting('autoDownloadQuality', 'best');
                    setShowQualityModal(false);
                  }}
                >
                  <Ionicons name="diamond" size={20} color={settings.autoDownloadQuality === 'best' ? '#6c5ce7' : '#fff'} />
                  <View style={styles.qualityOptionText}>
                    <Text style={[styles.qualityOptionTitle, settings.autoDownloadQuality === 'best' && styles.qualityOptionTitleSelected]}>
                      Melhor
                    </Text>
                    <Text style={styles.qualityOptionDesc}>
                      Sempre baixar na melhor qualidade disponível
                    </Text>
                  </View>
                  {settings.autoDownloadQuality === 'best' && (
                    <Ionicons name="checkmark-circle" size={20} color="#6c5ce7" />
                  )}
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.qualityOption, settings.autoDownloadQuality === 'medium' && styles.qualityOptionSelected]}
                  onPress={() => {
                    updateSetting('autoDownloadQuality', 'medium');
                    setShowQualityModal(false);
                  }}
                >
                  <Ionicons name="play-circle" size={20} color={settings.autoDownloadQuality === 'medium' ? '#6c5ce7' : '#fff'} />
                  <View style={styles.qualityOptionText}>
                    <Text style={[styles.qualityOptionTitle, settings.autoDownloadQuality === 'medium' && styles.qualityOptionTitleSelected]}>
                      Média
                    </Text>
                    <Text style={styles.qualityOptionDesc}>
                      Balanceio entre qualidade e tamanho do arquivo
                    </Text>
                  </View>
                  {settings.autoDownloadQuality === 'medium' && (
                    <Ionicons name="checkmark-circle" size={20} color="#6c5ce7" />
                  )}
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.qualityOption, settings.autoDownloadQuality === 'auto' && styles.qualityOptionSelected]}
                  onPress={() => {
                    updateSetting('autoDownloadQuality', 'auto');
                    setShowQualityModal(false);
                  }}
                >
                  <Ionicons name="flash" size={20} color={settings.autoDownloadQuality === 'auto' ? '#6c5ce7' : '#fff'} />
                  <View style={styles.qualityOptionText}>
                    <Text style={[styles.qualityOptionTitle, settings.autoDownloadQuality === 'auto' && styles.qualityOptionTitleSelected]}>
                      Automática
                    </Text>
                    <Text style={styles.qualityOptionDesc}>
                      Escolher automaticamente baseado na conexão
                    </Text>
                  </View>
                  {settings.autoDownloadQuality === 'auto' && (
                    <Ionicons name="checkmark-circle" size={20} color="#6c5ce7" />
                  )}
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>

        {/* Audio Mode Modal */}
        <Modal
          visible={showAudioModal}
          animationType="slide"
          transparent={true}
          onRequestClose={() => setShowAudioModal(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.qualityModal}>
              <View style={styles.qualityHeader}>
                <Text style={styles.qualityTitle}>Modo de Áudio</Text>
                <TouchableOpacity onPress={() => setShowAudioModal(false)}>
                  <Ionicons name="close" size={24} color="#fff" />
                </TouchableOpacity>
              </View>

              <Text style={styles.qualitySubtitle}>
                Escolha como o áudio dos vídeos deve interagir com outros apps
              </Text>

              <View style={styles.qualityOptions}>
                <TouchableOpacity
                  style={[styles.qualityOption, (settings.audioMode || 'mix') === 'mix' && styles.qualityOptionSelected]}
                  onPress={() => {
                    updateSetting('audioMode', 'mix');
                    setShowAudioModal(false);
                  }}
                >
                  <Ionicons name="musical-notes" size={20} color={(settings.audioMode || 'mix') === 'mix' ? '#6c5ce7' : '#fff'} />
                  <View style={styles.qualityOptionText}>
                    <Text style={[styles.qualityOptionTitle, (settings.audioMode || 'mix') === 'mix' && styles.qualityOptionTitleSelected]}>
                      Mix
                    </Text>
                    <Text style={styles.qualityOptionDesc}>
                      Reproduzir junto com outros áudios (recomendado)
                    </Text>
                  </View>
                  {(settings.audioMode || 'mix') === 'mix' && (
                    <Ionicons name="checkmark-circle" size={20} color="#6c5ce7" />
                  )}
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.qualityOption, settings.audioMode === 'duck' && styles.qualityOptionSelected]}
                  onPress={() => {
                    updateSetting('audioMode', 'duck');
                    setShowAudioModal(false);
                  }}
                >
                  <Ionicons name="volume-low" size={20} color={settings.audioMode === 'duck' ? '#6c5ce7' : '#fff'} />
                  <View style={styles.qualityOptionText}>
                    <Text style={[styles.qualityOptionTitle, settings.audioMode === 'duck' && styles.qualityOptionTitleSelected]}>
                      Duck
                    </Text>
                    <Text style={styles.qualityOptionDesc}>
                      Diminuir volume de outros apps durante reprodução
                    </Text>
                  </View>
                  {settings.audioMode === 'duck' && (
                    <Ionicons name="checkmark-circle" size={20} color="#6c5ce7" />
                  )}
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.qualityOption, settings.audioMode === 'exclusive' && styles.qualityOptionSelected]}
                  onPress={() => {
                    updateSetting('audioMode', 'exclusive');
                    setShowAudioModal(false);
                  }}
                >
                  <Ionicons name="volume-mute" size={20} color={settings.audioMode === 'exclusive' ? '#6c5ce7' : '#fff'} />
                  <View style={styles.qualityOptionText}>
                    <Text style={[styles.qualityOptionTitle, settings.audioMode === 'exclusive' && styles.qualityOptionTitleSelected]}>
                      Exclusivo
                    </Text>
                    <Text style={styles.qualityOptionDesc}>
                      Pausar outros áudios durante reprodução
                    </Text>
                  </View>
                  {settings.audioMode === 'exclusive' && (
                    <Ionicons name="checkmark-circle" size={20} color="#6c5ce7" />
                  )}
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>

        {/* Download Location Modal */}
        <Modal
          visible={showDownloadLocationModal}
          animationType="slide"
          transparent={true}
          onRequestClose={() => setShowDownloadLocationModal(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.qualityModal}>
              <View style={styles.qualityHeader}>
                <Text style={styles.qualityTitle}>Local de Download</Text>
                <TouchableOpacity onPress={() => setShowDownloadLocationModal(false)}>
                  <Ionicons name="close" size={24} color="#fff" />
                </TouchableOpacity>
              </View>

              <Text style={styles.qualitySubtitle}>
                Escolha onde salvar os vídeos baixados
              </Text>

              <View style={styles.qualityOptions}>
                <TouchableOpacity
                  style={[styles.qualityOption, styles.qualityOptionSelected]}
                  onPress={() => setShowDownloadLocationModal(false)}
                >
                  <Ionicons name="images" size={20} color="#6c5ce7" />
                  <View style={styles.qualityOptionText}>
                    <Text style={[styles.qualityOptionTitle, styles.qualityOptionTitleSelected]}>
                      Galeria do Dispositivo
                    </Text>
                    <Text style={styles.qualityOptionDesc}>
                      Salvar na galeria de fotos/vídeos (padrão)
                    </Text>
                  </View>
                  <Ionicons name="checkmark-circle" size={20} color="#6c5ce7" />
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.qualityOption}
                  onPress={() => {
                    Alert.alert('Info', 'Outras opções de local serão implementadas em futuras versões');
                    setShowDownloadLocationModal(false);
                  }}
                >
                  <Ionicons name="folder" size={20} color="#fff" />
                  <View style={styles.qualityOptionText}>
                    <Text style={styles.qualityOptionTitle}>
                      Pasta Personalizada
                    </Text>
                    <Text style={styles.qualityOptionDesc}>
                      Escolher pasta específica (em breve)
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>

        {/* Modal Sobre */}
        <Modal
          visible={showAbout}
          transparent={true}
          animationType="fade"
          onRequestClose={() => setShowAbout(false)}
        >
          <View style={styles.aboutOverlay}>
            <View style={styles.aboutContainer}>
              <View style={styles.aboutHeader}>
                <Text style={styles.aboutTitle}>Video Browser</Text>
                <TouchableOpacity onPress={() => setShowAbout(false)}>
                  <Ionicons name="close" size={24} color="#fff" />
                </TouchableOpacity>
              </View>
              
              <ScrollView style={styles.aboutContent}>
                <Text style={styles.aboutText}>
                  Video Browser é um navegador completo com capacidades avançadas de download de vídeos.
                </Text>
                
                <Text style={styles.aboutSection}>Recursos:</Text>
                <Text style={styles.aboutFeature}>• Navegação com múltiplas abas</Text>
                <Text style={styles.aboutFeature}>• Download de vídeos de qualquer site</Text>
                <Text style={styles.aboutFeature}>• Seleção de qualidade de vídeo</Text>
                <Text style={styles.aboutFeature}>• Bloqueador de anúncios integrado</Text>
                <Text style={styles.aboutFeature}>• Proteção de privacidade avançada</Text>
                <Text style={styles.aboutFeature}>• Gerenciador de downloads</Text>
                <Text style={styles.aboutFeature}>• Sistema de favoritos</Text>
                
                <Text style={styles.aboutVersion}>Versão 1.0.0</Text>
                <Text style={styles.aboutCopyright}>© 2024 Video Browser</Text>
              </ScrollView>
            </View>
          </View>
        </Modal>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1a1a2e',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingTop: 50,
    backgroundColor: '#1a1a2e',
  },
  title: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
  },
  section: {
    marginBottom: 30,
  },
  sectionTitle: {
    color: '#6c5ce7',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 15,
    marginHorizontal: 20,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 15,
    paddingHorizontal: 20,
    backgroundColor: '#2d2d44',
    marginHorizontal: 20,
    marginBottom: 2,
    borderRadius: 8,
  },
  settingIcon: {
    marginRight: 15,
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  settingSubtitle: {
    color: '#888',
    fontSize: 12,
    marginTop: 2,
  },
  aboutOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  aboutContainer: {
    backgroundColor: '#1a1a2e',
    borderRadius: 15,
    width: width * 0.9,
    maxHeight: '80%',
  },
  aboutHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#2d2d44',
  },
  aboutTitle: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  aboutContent: {
    padding: 20,
  },
  aboutText: {
    color: '#fff',
    fontSize: 16,
    lineHeight: 24,
    marginBottom: 20,
  },
  aboutSection: {
    color: '#6c5ce7',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  aboutFeature: {
    color: '#fff',
    fontSize: 14,
    marginBottom: 5,
  },
  aboutVersion: {
    color: '#888',
    fontSize: 14,
    marginTop: 20,
    textAlign: 'center',
  },
  aboutCopyright: {
    color: '#888',
    fontSize: 12,
    textAlign: 'center',
    marginTop: 5,
  },
  // Quality Modal Styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  qualityModal: {
    backgroundColor: '#1a1a2e',
    borderRadius: 15,
    padding: 20,
    width: width * 0.9,
    maxHeight: '80%',
  },
  qualityHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  qualityTitle: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
  },
  qualitySubtitle: {
    color: '#888',
    fontSize: 14,
    marginBottom: 20,
    textAlign: 'center',
  },
  qualityOptions: {
    gap: 10,
  },
  qualityOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    backgroundColor: '#2a2a3e',
    borderRadius: 10,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  qualityOptionSelected: {
    borderColor: '#6c5ce7',
    backgroundColor: '#2a2a4e',
  },
  qualityOptionText: {
    flex: 1,
    marginLeft: 15,
  },
  qualityOptionTitle: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  qualityOptionTitleSelected: {
    color: '#6c5ce7',
  },
  qualityOptionDesc: {
    color: '#888',
    fontSize: 12,
    marginTop: 2,
  },
});

export default Settings;
