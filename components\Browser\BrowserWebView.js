import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Alert,
  Dimensions,
  StatusBar,
  Text,
  Modal,
  ScrollView
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { WebView } from 'react-native-webview';
import { Ionicons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import VideoThumbnailContextMenu from './VideoThumbnailContextMenu';
import AudioSessionManager from '../../services/AudioSessionManager';

const { width, height } = Dimensions.get('window');

const BrowserWebView = ({
  initialUrl = 'https://www.google.com',
  onVideoDetected,
  onDownloadRequest,
  onTitleChange,
  onUrlChange,
  showDownloadButton = true
}) => {
  const [url, setUrl] = useState(initialUrl);
  const [currentUrl, setCurrentUrl] = useState(initialUrl);
  const [canGoBack, setCanGoBack] = useState(false);
  const [canGoForward, setCanGoForward] = useState(false);
  const [loading, setLoading] = useState(false);
  const [showMenu, setShowMenu] = useState(false);
  const [history, setHistory] = useState([]);
  const [bookmarks, setBookmarks] = useState([]);
  const [showBookmarks, setShowBookmarks] = useState(false);
  const [pageTitle, setPageTitle] = useState('');
  const [isSecure, setIsSecure] = useState(false);
  const [showThumbnailMenu, setShowThumbnailMenu] = useState(false);
  const [thumbnailData, setThumbnailData] = useState(null);
  const [showQualitySelector, setShowQualitySelector] = useState(false);
  const [availableQualities, setAvailableQualities] = useState([]);
  const [currentVideoData, setCurrentVideoData] = useState(null);

  const webViewRef = useRef(null);

  useEffect(() => {
    loadBookmarks();
    initializeAudioSession();
  }, []);

  const initializeAudioSession = async () => {
    try {
      console.log('[WEBVIEW] Initializing audio session for concurrent playback');
      await AudioSessionManager.initialize();
      // Set to mix mode to allow concurrent playback with other apps
      await AudioSessionManager.setAudioMode('mix');
      console.log('[WEBVIEW] Audio session configured for concurrent playback');
    } catch (error) {
      console.error('[WEBVIEW] Failed to initialize audio session:', error);
      // Continue without audio session - WebView will still work
      console.log('[WEBVIEW] Continuing without audio session configuration');
    }
  };

  const loadBookmarks = async () => {
    try {
      const savedBookmarks = await AsyncStorage.getItem('browser_bookmarks');
      if (savedBookmarks) {
        setBookmarks(JSON.parse(savedBookmarks));
      }
    } catch (error) {
      console.error('Erro ao carregar favoritos:', error);
    }
  };

  const saveBookmarks = async (newBookmarks) => {
    try {
      await AsyncStorage.setItem('browser_bookmarks', JSON.stringify(newBookmarks));
      setBookmarks(newBookmarks);
    } catch (error) {
      console.error('Erro ao salvar favoritos:', error);
    }
  };

  const addBookmark = () => {
    const newBookmark = {
      id: Date.now().toString(),
      title: pageTitle || 'Página sem título',
      url: currentUrl,
      timestamp: new Date().toISOString()
    };
    
    const updatedBookmarks = [...bookmarks, newBookmark];
    saveBookmarks(updatedBookmarks);
    Alert.alert('Sucesso', 'Página adicionada aos favoritos!');
  };

  const removeBookmark = (id) => {
    const updatedBookmarks = bookmarks.filter(bookmark => bookmark.id !== id);
    saveBookmarks(updatedBookmarks);
  };

  const navigateToUrl = (targetUrl) => {
    let formattedUrl = targetUrl.trim();
    
    if (!formattedUrl.startsWith('http://') && !formattedUrl.startsWith('https://')) {
      if (formattedUrl.includes('.') && !formattedUrl.includes(' ')) {
        formattedUrl = 'https://' + formattedUrl;
      } else {
        formattedUrl = `https://www.google.com/search?q=${encodeURIComponent(formattedUrl)}`;
      }
    }
    
    setUrl(formattedUrl);
    setCurrentUrl(formattedUrl);
  };

  const handleNavigationStateChange = (navState) => {
    setCurrentUrl(navState.url);
    setCanGoBack(navState.canGoBack);
    setCanGoForward(navState.canGoForward);
    setLoading(navState.loading);
    setPageTitle(navState.title);
    setIsSecure(navState.url.startsWith('https://'));
    
    if (onTitleChange) {
      onTitleChange(navState.title);
    }
    
    if (onUrlChange) {
      onUrlChange(navState.url);
    }
    
    // Adicionar ao histórico
    if (navState.url && navState.url !== currentUrl) {
      setHistory(prevHistory => {
        const newHistory = [...prevHistory, {
          url: navState.url,
          title: navState.title,
          timestamp: Date.now()
        }];
        return newHistory.slice(-50); // Manter apenas os últimos 50
      });
    }
  };

  const detectVideoInPage = (html) => {
    // Detectar vídeos na página com padrões mais específicos
    const videoPatterns = [
      // Arquivos de vídeo diretos
      /\.(mp4|webm|avi|mov|mkv|flv|wmv|m4v)(\?[^"'\s]*)?/gi,
      // Plataformas de vídeo conhecidas
      /youtube\.com\/watch\?v=/gi,
      /youtu\.be\//gi,
      /vimeo\.com\/\d+/gi,
      /dailymotion\.com\/video/gi,
      /twitch\.tv\/videos/gi,
      // Tags de vídeo HTML
      /<video[^>]*src=/gi,
      /<source[^>]*src=[^>]*\.(mp4|webm|avi|mov)/gi,
      // Streams HLS/DASH
      /\.m3u8/gi,
      /\.mpd/gi
    ];

    const hasVideo = videoPatterns.some(pattern => pattern.test(html));
    if (hasVideo && onVideoDetected) {
      console.log('🎥 Vídeo detectado na página:', currentUrl);
      onVideoDetected(currentUrl);
    }
  };

  const handleMessage = (event) => {
    try {
      const data = JSON.parse(event.nativeEvent.data);
      console.log('[RN] Message received from WebView:', data.type);
      
      if (data.type === 'pageContent') {
        detectVideoInPage(data.content);
      } else if (data.type === 'videoThumbnailLongPress') {
        console.log('[RN] Video thumbnail long press received:', data.thumbnailData);
        setThumbnailData(data.thumbnailData);
        setShowThumbnailMenu(true);
        console.log('[RN] Context menu should now be visible');
      }
    } catch (error) {
      console.error('[RN] Erro ao processar mensagem:', error);
    }
  };

  const handleDownload = () => {
    if (onDownloadRequest) {
      // Usar currentUrl (URL real da página) em vez de url (URL do input)
      const pageUrl = currentUrl || url;
      console.log('🎯 Iniciando download do vídeo principal da página:', pageUrl);
      console.log('🎯 URL do input:', url, '| URL atual da página:', currentUrl);
      onDownloadRequest(pageUrl);
    } else {
      Alert.alert('Download', 'Funcionalidade de download será implementada');
    }
  };

  const handleThumbnailDownload = (thumbnailData, quality) => {
    console.log('[WEBVIEW] Thumbnail download requested:', thumbnailData, quality);
    if (onDownloadRequest) {
      onDownloadRequest(thumbnailData.videoUrl, quality, thumbnailData);
    } else {
      Alert.alert('Download', `Download solicitado para: ${thumbnailData.title}`);
    }
  };

  const handleQualitySelect = (quality) => {
    console.log('[WEBVIEW] Quality selected:', quality);
    setShowQualitySelector(false);
    if (currentVideoData && onDownloadRequest) {
      onDownloadRequest(currentVideoData.url, quality, currentVideoData);
    }
  };

  const injectedJavaScript = `
    (function() {
      console.log('[INJECT] Starting enhanced thumbnail detection');

      // Test basic functionality first
      if (!window.ReactNativeWebView) {
        console.log('[INJECT] ERROR: ReactNativeWebView not available');
        return;
      }

      console.log('[INJECT] ReactNativeWebView is available');

      // Simple video thumbnail detection
      function isVideoThumbnail(element) {
        if (!element || element.tagName !== 'IMG') return false;

        const src = element.src || '';
        const alt = element.alt || '';
        const className = element.className || '';

        // Simple patterns for video thumbnails
        const videoPatterns = [
          /thumbnail/i,
          /preview/i,
          /video/i,
          /ytimg/i,
          /play/i
        ];

        const textToCheck = [src, alt, className].join(' ');
        return videoPatterns.some(pattern => pattern.test(textToCheck));
      }

      // Extract video URL from thumbnail context
      function extractVideoUrl(element) {
        let currentElement = element;

        // Look for video URL in element hierarchy
        for (let i = 0; i < 5; i++) {
          if (!currentElement) break;

          // Check for direct href
          if (currentElement.href) {
            return currentElement.href;
          }

          // Check for data attributes
          const dataUrl = currentElement.getAttribute('data-url') ||
                         currentElement.getAttribute('data-video-url') ||
                         currentElement.getAttribute('data-href');
          if (dataUrl) return dataUrl;

          // Look for nearby links
          const nearbyLink = currentElement.querySelector('a[href]');
          if (nearbyLink && nearbyLink.href) {
            return nearbyLink.href;
          }

          currentElement = currentElement.parentElement;
        }

        // Fallback to current page URL
        return window.location.href;
      }

      // Extract video title
      function extractVideoTitle(element) {
        // Try various sources for title
        const sources = [
          () => element.alt,
          () => element.title,
          () => element.getAttribute('aria-label'),
          () => {
            const parent = element.parentElement;
            return parent ? parent.getAttribute('aria-label') : null;
          },
          () => {
            // Look for nearby text elements
            const parent = element.parentElement;
            if (!parent) return null;

            const textElements = parent.querySelectorAll('h1, h2, h3, h4, h5, h6, p, span, div');
            for (const textEl of textElements) {
              const text = textEl.textContent?.trim();
              if (text && text.length > 5 && text.length < 200) {
                return text;
              }
            }
            return null;
          },
          () => document.title
        ];

        for (const source of sources) {
          try {
            const title = source();
            if (title && title.trim()) return title.trim();
          } catch (e) {
            // Continue to next source
          }
        }

        return 'Video';
      }

      // Detect platform from URL
      function detectPlatform(url) {
        const hostname = new URL(url || window.location.href).hostname.toLowerCase();

        if (hostname.includes('youtube.com') || hostname.includes('youtu.be')) return 'youtube';
        if (hostname.includes('vimeo.com')) return 'vimeo';
        if (hostname.includes('instagram.com')) return 'instagram';
        if (hostname.includes('tiktok.com')) return 'tiktok';
        if (hostname.includes('facebook.com')) return 'facebook';
        if (hostname.includes('twitter.com') || hostname.includes('x.com')) return 'twitter';
        if (hostname.includes('dailymotion.com')) return 'dailymotion';
        if (hostname.includes('twitch.tv')) return 'twitch';

        return 'generic';
      }

      // Enhanced message sending
      function sendMessage(element, x, y) {
        console.log('[INJECT] Sending enhanced message for element:', element.tagName);

        const videoUrl = extractVideoUrl(element);
        const title = extractVideoTitle(element);
        const platform = detectPlatform(videoUrl);

        const data = {
          title: title,
          videoUrl: videoUrl,
          thumbnailUrl: element.src || null,
          x: x || 100,
          y: y || 100,
          platform: platform,
          isVideoThumbnail: isVideoThumbnail(element)
        };

        console.log('[INJECT] Thumbnail data:', data);

        window.ReactNativeWebView.postMessage(JSON.stringify({
          type: 'videoThumbnailLongPress',
          thumbnailData: data
        }));

        console.log('[INJECT] Enhanced message sent');
      }

      // Add simple click listeners to all images
      function addClickListeners() {
        console.log('[INJECT] Adding click listeners');

        const images = document.querySelectorAll('img');
        console.log('[INJECT] Found', images.length, 'images');

        images.forEach((img, index) => {
          console.log('[INJECT] Adding listeners to image', index);

          // Add long press listener (touch devices)
          let longPressTimer;
          img.addEventListener('touchstart', function(e) {
            longPressTimer = setTimeout(() => {
              console.log('[INJECT] Image long pressed');
              e.preventDefault();
              e.stopPropagation();
              const touch = e.touches[0];
              sendMessage(img, touch.clientX, touch.clientY);
            }, 500);
          });

          img.addEventListener('touchend', function(e) {
            clearTimeout(longPressTimer);
          });

          img.addEventListener('touchmove', function(e) {
            clearTimeout(longPressTimer);
          });

          // Add right-click listener (desktop)
          img.addEventListener('contextmenu', function(e) {
            console.log('[INJECT] Image right-clicked');
            e.preventDefault();
            e.stopPropagation();
            sendMessage(img, e.clientX, e.clientY);
          });

          // Add click listener for testing
          img.addEventListener('click', function(e) {
            console.log('[INJECT] Image clicked');
            // Don't prevent default for normal clicks unless it's a video thumbnail
            if (isVideoThumbnail(img)) {
              e.preventDefault();
              e.stopPropagation();
              sendMessage(img, e.clientX, e.clientY);
            }
          });
        });

        console.log('[INJECT] Click listeners added to', images.length, 'images');
      }

      // Run immediately
      addClickListeners();

      // Run when DOM is ready
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', addClickListeners);
      }

      // Run after a delay for dynamic content
      setTimeout(addClickListeners, 2000);
      
      // Configure web audio for concurrent playback
      function configureWebAudioForConcurrentPlayback() {
        console.log('[INJECT] Configuring web audio for concurrent playback');
        
        try {
          // Configure all video elements for concurrent playback
          const videos = document.querySelectorAll('video');
          videos.forEach(video => {
            // Prevent videos from pausing other audio
            video.setAttribute('playsinline', 'true');
            video.setAttribute('webkit-playsinline', 'true');

            // Configure for concurrent playback
            video.setAttribute('preload', 'metadata');
            video.muted = false; // Ensure audio is not muted

            // Override pause behavior to not affect other audio
            const originalPause = video.pause;
            video.pause = function() {
              console.log('[INJECT] Video pause called - maintaining concurrent audio');
              return originalPause.call(this);
            };

            // Add event listeners to monitor playback
            video.addEventListener('play', function() {
              console.log('[INJECT] Video started playing - concurrent mode active');
            });

            video.addEventListener('pause', function() {
              console.log('[INJECT] Video paused - other audio should continue');
            });
          });

          // Configure Web Audio API for concurrent playback if available
          if (window.AudioContext || window.webkitAudioContext) {
            console.log('[INJECT] Web Audio API available - configuring for concurrent playback');
          }

          console.log('[INJECT] Web audio configured for concurrent playback');
        } catch (error) {
          console.log('[INJECT] Error configuring web audio:', error);
        }
      }
      
      // Run audio configuration
      configureWebAudioForConcurrentPlayback();
      
      // Re-run audio configuration when new content loads
      const observer = new MutationObserver(function(mutations) {
        let hasNewVideos = false;
        mutations.forEach(function(mutation) {
          if (mutation.addedNodes) {
            mutation.addedNodes.forEach(function(node) {
              if (node.nodeType === 1) { // Element node
                if (node.tagName === 'VIDEO' || (node.querySelector && node.querySelector('video'))) {
                  hasNewVideos = true;
                }
              }
            });
          }
        });
        
        if (hasNewVideos) {
          console.log('[INJECT] New videos detected, reconfiguring audio');
          setTimeout(configureWebAudioForConcurrentPlayback, 100);
          setTimeout(addClickListeners, 100);
        }
      });
      
      // Start observing
      observer.observe(document.body, {
        childList: true,
        subtree: true
      });

      console.log('[INJECT] Injection complete with concurrent playback support');
    })();
  `;

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#1a1a2e" />

      {/* Barra de navegação com safe area */}
      <SafeAreaView edges={['left', 'right']} style={styles.navigationBarContainer}>
        <View style={styles.navigationBar}>
          <View style={styles.urlContainer}>
            <View style={styles.securityIndicator}>
              <Ionicons
                name={isSecure ? "lock-closed" : "lock-open"}
                size={16}
                color={isSecure ? "#4CAF50" : "#FF9800"}
              />
            </View>

            <TextInput
              style={styles.urlInput}
              value={url}
              onChangeText={setUrl}
              onSubmitEditing={() => navigateToUrl(url)}
              placeholder="Digite uma URL ou pesquise..."
              placeholderTextColor="#888"
              autoCapitalize="none"
              autoCorrect={false}
              selectTextOnFocus
            />

            <TouchableOpacity
              style={styles.refreshButton}
              onPress={() => webViewRef.current?.reload()}
            >
              <Ionicons name="refresh" size={20} color="#fff" />
            </TouchableOpacity>
          </View>
        </View>
      </SafeAreaView>

      {/* Loading indicator */}
      {loading && (
        <View style={styles.loadingContainer}>
          <Ionicons name="hourglass" size={16} color="#6c5ce7" />
          <Text style={styles.loadingText}>Carregando...</Text>
        </View>
      )}

      {/* Botão de download flutuante */}
      {showDownloadButton && (
        <TouchableOpacity
          style={styles.downloadButton}
          onPress={handleDownload}
        >
          <Ionicons name="download" size={24} color="#fff" />
        </TouchableOpacity>
      )}

      {/* Test button for context menu */}
      <TouchableOpacity
        style={styles.testButton}
        onPress={() => {
          console.log('[TEST] Manual context menu trigger');
          setThumbnailData({
            title: 'Test Video',
            videoUrl: 'https://example.com/test.mp4',
            thumbnailUrl: null,
            x: 100,
            y: 100,
            platform: 'test'
          });
          setShowThumbnailMenu(true);
        }}
      >
        <Ionicons name="bug" size={20} color="#fff" />
      </TouchableOpacity>

      {/* Audio mode indicator */}
      <View style={styles.audioModeIndicator}>
        <Ionicons name="musical-notes" size={16} color="#6c5ce7" />
        <Text style={styles.audioModeText}>Concurrent Playback Mode</Text>
      </View>

      {/* WebView */}
      <WebView
        ref={webViewRef}
        source={{ uri: url }}
        style={styles.webview}
        onNavigationStateChange={handleNavigationStateChange}
        onMessage={handleMessage}
        injectedJavaScript={injectedJavaScript}
        onLoadStart={() => {
          setLoading(true);
          console.log('[WEBVIEW] Page load started - maintaining concurrent audio session');
        }}
        onLoadEnd={() => {
          setLoading(false);
          console.log('[WEBVIEW] Page load completed - ensuring concurrent playback');
          // Re-initialize audio session to ensure concurrent playback
          setTimeout(async () => {
            try {
              await AudioSessionManager.prepareForVideoPlayback();
            } catch (error) {
              console.log('[WEBVIEW] Audio session preparation failed, continuing without it');
            }
          }, 1000);
        }}
        onError={(syntheticEvent) => {
          const { nativeEvent } = syntheticEvent;
          console.error('[WEBVIEW] WebView error: ', nativeEvent);
        }}
        onShouldStartLoadWithRequest={(request) => {
          console.log('[WEBVIEW] Loading request:', request.url);
          return true;
        }}
        allowsInlineMediaPlayback={true}
        mediaPlaybackRequiresUserAction={false}
        allowsFullscreenVideo={true}
        mixedContentMode="compatibility"
        allowsAirPlayForMediaPlayback={true}
        allowsPictureInPictureMediaPlayback={true}
        // Configurações específicas para reprodução simultânea
        ignoreSilentHardwareSwitch={false}
        allowsProtectedMedia={true}
        startInLoadingState={true}
        scalesPageToFit={true}
        bounces={false}
        scrollEnabled={true}
        javaScriptEnabled={true}
        domStorageEnabled={true}
        cacheEnabled={true}
        // Enhanced concurrent playback support
        allowsBackForwardNavigationGestures={true}
        decelerationRate={0.998}
        // iOS specific concurrent playback settings
        allowsLinkPreview={false}
        dataDetectorTypes={[]}
        // Android specific settings for better performance
        androidHardwareAccelerationDisabled={false}
        androidLayerType="hardware"
      />

      {/* Context Menu para thumbnails */}
      <VideoThumbnailContextMenu
        visible={showThumbnailMenu}
        onClose={() => setShowThumbnailMenu(false)}
        thumbnailData={thumbnailData}
        onDownloadRequest={handleThumbnailDownload}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1a1a2e',
  },
  navigationBarContainer: {
    backgroundColor: '#1a1a2e',
  },
  navigationBar: {
    flexDirection: 'row',
    paddingHorizontal: 10,
    paddingVertical: 8,
    alignItems: 'center',
  },
  urlContainer: {
    flex: 1,
    flexDirection: 'row',
    backgroundColor: '#2d2d44',
    borderRadius: 8,
    paddingHorizontal: 12,
    alignItems: 'center',
  },
  securityIndicator: {
    marginRight: 8,
  },
  urlInput: {
    flex: 1,
    color: '#fff',
    fontSize: 16,
    paddingVertical: 8,
  },
  refreshButton: {
    marginLeft: 10,
    padding: 8,
    backgroundColor: '#6c5ce7',
    borderRadius: 6,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    backgroundColor: 'rgba(108, 92, 231, 0.1)',
  },
  loadingText: {
    color: '#6c5ce7',
    marginLeft: 8,
    fontSize: 14,
  },
  webview: {
    flex: 1,
  },
  downloadButton: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    backgroundColor: '#6c5ce7',
    borderRadius: 30,
    padding: 15,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    zIndex: 1000,
  },
  testButton: {
    position: 'absolute',
    bottom: 80,
    right: 20,
    backgroundColor: '#FF9800',
    borderRadius: 25,
    padding: 12,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    zIndex: 1000,
  },
  audioModeIndicator: {
    position: 'absolute',
    top: 100,
    left: 20,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(108, 92, 231, 0.1)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: 'rgba(108, 92, 231, 0.3)',
  },
  audioModeText: {
    color: '#6c5ce7',
    fontSize: 12,
    marginLeft: 4,
    fontWeight: '600',
  },
});

export default BrowserWebView;
